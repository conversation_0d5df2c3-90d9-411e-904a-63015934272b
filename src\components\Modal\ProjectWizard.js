"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Button from '../Button';

const ProjectWizard = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedServices, setSelectedServices] = useState([]);

  const services = [
    'Logo Design',
    'Brand Identity',
    'Website Design',
    'UI/UX Design',
    'Mobile App Design',
    'Print Design',
    'Packaging Design',
    'Social Media Design'
  ];

  const handleServiceToggle = (service) => {
    setSelectedServices(prev => 
      prev.includes(service) 
        ? prev.filter(s => s !== service)
        : [...prev, service]
    );
  };

  const nextStep = () => {
    setCurrentStep(prev => prev + 1);
  };

  const prevStep = () => {
    setCurrentStep(prev => prev - 1);
  };

  // Step content variants for smooth transitions
  const stepVariants = {
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -20 }
  };

  return (
    <div className="p-8">
      {/* Progress Indicator */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <span className="text-sm text-secondary/60">Step {currentStep} of 3</span>
          <span className="text-sm text-secondary/60">{Math.round((currentStep / 3) * 100)}% Complete</span>
        </div>
        <div className="w-full bg-secondary/10 rounded-full h-2">
          <motion.div 
            className="bg-accent h-2 rounded-full"
            initial={{ width: "33%" }}
            animate={{ width: `${(currentStep / 3) * 100}%` }}
            transition={{ duration: 0.3 }}
          />
        </div>
      </div>

      {/* Step Content */}
      <AnimatePresence mode="wait">
        {currentStep === 1 && (
          <motion.div
            key="step1"
            variants={stepVariants}
            initial="initial"
            animate="animate"
            exit="exit"
            transition={{ duration: 0.3 }}
          >
            <h2 className="text-3xl font-heading font-bold text-secondary mb-4">
              Let's Get Started!
            </h2>
            <p className="text-secondary/80 mb-8 text-lg">
              I'm excited to help bring your vision to life. This quick wizard will help me understand your project needs and provide you with the best possible solution.
            </p>
            <div className="space-y-4 mb-8">
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-accent rounded-full flex items-center justify-center">
                  <span className="text-white text-sm">✓</span>
                </div>
                <span className="text-secondary/80">Quick 3-step process</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-accent rounded-full flex items-center justify-center">
                  <span className="text-white text-sm">✓</span>
                </div>
                <span className="text-secondary/80">Personalized recommendations</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-accent rounded-full flex items-center justify-center">
                  <span className="text-white text-sm">✓</span>
                </div>
                <span className="text-secondary/80">Free consultation included</span>
              </div>
            </div>
            <Button onClick={nextStep} variant="primary" size="lg">
              Get Started
            </Button>
          </motion.div>
        )}

        {currentStep === 2 && (
          <motion.div
            key="step2"
            variants={stepVariants}
            initial="initial"
            animate="animate"
            exit="exit"
            transition={{ duration: 0.3 }}
          >
            <h2 className="text-3xl font-heading font-bold text-secondary mb-4">
              What Services Do You Need?
            </h2>
            <p className="text-secondary/80 mb-8">
              Select all the services you're interested in. Don't worry, we can always adjust this later.
            </p>
            
            <div className="grid grid-cols-2 gap-3 mb-8">
              {services.map((service) => (
                <button
                  key={service}
                  onClick={() => handleServiceToggle(service)}
                  className={`p-4 rounded-lg border-2 transition-all duration-200 text-left ${
                    selectedServices.includes(service)
                      ? 'border-accent bg-accent/10 text-secondary'
                      : 'border-secondary/20 hover:border-secondary/40 text-secondary/80 hover:text-secondary'
                  }`}
                >
                  <span className="font-medium">{service}</span>
                </button>
              ))}
            </div>

            <div className="flex space-x-4">
              <Button onClick={prevStep} variant="outline">
                Back
              </Button>
              <Button 
                onClick={nextStep} 
                variant="primary"
                disabled={selectedServices.length === 0}
              >
                Continue ({selectedServices.length} selected)
              </Button>
            </div>
          </motion.div>
        )}

        {currentStep === 3 && (
          <motion.div
            key="step3"
            variants={stepVariants}
            initial="initial"
            animate="animate"
            exit="exit"
            transition={{ duration: 0.3 }}
          >
            <h2 className="text-3xl font-heading font-bold text-secondary mb-4">
              Almost Done!
            </h2>
            <p className="text-secondary/80 mb-6">
              Great choices! Here's what you've selected:
            </p>
            
            <div className="bg-secondary/5 rounded-lg p-6 mb-8">
              <h3 className="font-semibold text-secondary mb-3">Selected Services:</h3>
              <div className="flex flex-wrap gap-2">
                {selectedServices.map((service) => (
                  <span 
                    key={service}
                    className="px-3 py-1 bg-accent/20 text-accent rounded-full text-sm"
                  >
                    {service}
                  </span>
                ))}
              </div>
            </div>

            <p className="text-secondary/80 mb-8">
              The next step would be our detailed project form, but for now this is just a demo! 
              In the real version, you'd provide project details, timeline, and budget information.
            </p>

            <div className="flex space-x-4">
              <Button onClick={prevStep} variant="outline">
                Back
              </Button>
              <Button variant="primary">
                Continue to Form (Demo)
              </Button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ProjectWizard;
