"use client";

import React from 'react';
import Button from '../Button';

const ResumeContent = () => {
  const handleDownloadPDF = () => {
    // TODO: Implement PDF download functionality
    console.log('Download PDF clicked');
  };

  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-heading font-bold text-secondary mb-2">
          Cretu Teodor
        </h1>
        <p className="text-xl text-secondary/80 mb-4">
          UI/UX Designer & Frontend Developer
        </p>
        <Button 
          onClick={handleDownloadPDF}
          variant="secondary"
          size="sm"
          className="mb-6"
        >
          Download PDF
        </Button>
      </div>

      {/* Resume Content - Placeholder for now */}
      <div className="space-y-8">
        {/* Experience Section */}
        <section>
          <h2 className="text-2xl font-heading font-bold text-secondary mb-4 border-b border-secondary/20 pb-2">
            Experience
          </h2>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold text-secondary">Senior UI/UX Designer</h3>
              <p className="text-secondary/80">Company Name • 2022 - Present</p>
              <p className="text-secondary/70 mt-2">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-secondary">Frontend Developer</h3>
              <p className="text-secondary/80">Another Company • 2020 - 2022</p>
              <p className="text-secondary/70 mt-2">
                Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
              </p>
            </div>
          </div>
        </section>

        {/* Skills Section */}
        <section>
          <h2 className="text-2xl font-heading font-bold text-secondary mb-4 border-b border-secondary/20 pb-2">
            Skills
          </h2>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="text-lg font-semibold text-secondary mb-2">Design</h3>
              <ul className="text-secondary/80 space-y-1">
                <li>• UI/UX Design</li>
                <li>• Brand Identity</li>
                <li>• Logo Design</li>
                <li>• Prototyping</li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-secondary mb-2">Development</h3>
              <ul className="text-secondary/80 space-y-1">
                <li>• React/Next.js</li>
                <li>• JavaScript</li>
                <li>• Tailwind CSS</li>
                <li>• Framer Motion</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Education Section */}
        <section>
          <h2 className="text-2xl font-heading font-bold text-secondary mb-4 border-b border-secondary/20 pb-2">
            Education
          </h2>
          <div>
            <h3 className="text-lg font-semibold text-secondary">Bachelor's in Design</h3>
            <p className="text-secondary/80">University Name • 2016 - 2020</p>
          </div>
        </section>
      </div>
    </div>
  );
};

export default ResumeContent;
