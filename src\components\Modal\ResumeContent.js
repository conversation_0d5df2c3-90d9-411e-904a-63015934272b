"use client";

import React from 'react';
import <PERSON><PERSON> from '../Button';

const ResumeContent = () => {
  const handleDownloadPDF = () => {
    // TODO: Implement PDF download functionality
    console.log('Download PDF clicked');
  };

  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-heading font-bold text-secondary mb-2">
          <PERSON>od<PERSON>-<PERSON><PERSON><PERSON>
        </h1>
        <p className="text-xl text-secondary/80 mb-4">
          Multimedia Artist & Creative Professional
        </p>
        <Button
          onClick={handleDownloadPDF}
          variant="secondary"
          size="sm"
          className="mb-6"
        >
          Download PDF
        </Button>
      </div>

      {/* Professional Summary */}
      <div className="mb-8">
        <p className="text-secondary/80 leading-relaxed">
          I'm a versatile multimedia artist with experience across digital entertainment, marketing, and creative production. My work blends graphic design, 3D rendering, branding, and motion graphics, supported by an efficient, adaptable workflow. I move seamlessly between tools and disciplines to deliver production-ready assets with speed and precision. Guided by curiosity and continuous learning, I've also explored game design and interactive media to expand my creative range.
        </p>
      </div>

      {/* Resume Content */}
      <div className="space-y-8">
        {/* Experience Section */}
        <section>
          <h2 className="text-2xl font-heading font-bold text-secondary mb-4 border-b border-secondary/20 pb-2">
            Experience
          </h2>
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-secondary">Video & Animation Designer</h3>
              <p className="text-secondary/80 mb-2">vidaXL.com • November 2022 - Present • Bucharest</p>
              <p className="text-secondary/70 text-sm">
                vidaXL is an international online retailer, with headquarters in Venlo, The Netherlands.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-secondary">Graphic Designer</h3>
              <p className="text-secondary/80 mb-2">vidaXL.com • September 2021 - October 2022 • Bucharest</p>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-secondary">Graphic Designer</h3>
              <p className="text-secondary/80 mb-2">Sound Design Theatre Company • September 2019 - January 2021</p>
              <p className="text-secondary/70 text-sm">
                Sound Design Theatre Company focuses on the production of musical shows in Romania.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-secondary">Graphic Designer</h3>
              <p className="text-secondary/80 mb-2">Newton Coin Project • June 2018 - December 2018 • Remote</p>
              <p className="text-secondary/70 text-sm">
                Cryptocurrency created to help fund research in the medical and renewable energy fields.
              </p>
            </div>
          </div>
        </section>

        {/* Education Section */}
        <section>
          <h2 className="text-2xl font-heading font-bold text-secondary mb-4 border-b border-secondary/20 pb-2">
            Education
          </h2>
          <div>
            <h3 className="text-lg font-semibold text-secondary">Animation & Game Design</h3>
            <p className="text-secondary/80 mb-3">SAE Institute (Media Academy) • October 2016 - September 2017 • Bucharest</p>
            <ul className="text-secondary/70 space-y-1 text-sm">
              <li>• Game design theory, level design, and character design</li>
              <li>• Classic and digital drawing fundamentals</li>
              <li>• 3D software proficiency: 3ds Max, Autodesk Maya, ZBrush</li>
              <li>• Procedural texturing, digital environment design, and animation basics</li>
            </ul>
          </div>
        </section>

        {/* Skills Section */}
        <section>
          <h2 className="text-2xl font-heading font-bold text-secondary mb-4 border-b border-secondary/20 pb-2">
            Skills
          </h2>
          <div className="space-y-6">
            {/* Creative Software */}
            <div>
              <h3 className="text-lg font-semibold text-secondary mb-3">Creative Software</h3>
              <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3">
                {[
                  'Adobe Photoshop',
                  'Adobe Illustrator',
                  'Adobe InDesign',
                  'Adobe After Effects',
                  'Adobe Lightroom',
                  'Blender',
                  'Unreal Engine 5',
                  'ZBrush',
                  'VS Code',
                  'GPT (AI)',
                  'MS Office'
                ].map((software, index) => (
                  <div key={index} className="bg-secondary/5 rounded-lg p-2 text-center">
                    <span className="text-secondary/80 text-sm">{software}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Professional Skills & Languages */}
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-secondary mb-3">Professional Skills</h3>
                <ul className="text-secondary/80 space-y-1">
                  <li>• Collaboration</li>
                  <li>• Adaptability</li>
                  <li>• Problem-Solving</li>
                  <li>• Communication</li>
                  <li>• Organization</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-secondary mb-3">Languages</h3>
                <ul className="text-secondary/80 space-y-1">
                  <li>• English (Full Professional Proficiency)</li>
                  <li>• Romanian (Native)</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Interests Section */}
        <section>
          <h2 className="text-2xl font-heading font-bold text-secondary mb-4 border-b border-secondary/20 pb-2">
            Interests
          </h2>
          <div className="flex flex-wrap gap-3">
            {[
              'Graphic Design',
              'Music Composition',
              'Animation',
              'Scriptwriting',
              'Level Design'
            ].map((interest, index) => (
              <span
                key={index}
                className="bg-accent/10 text-accent px-3 py-1 rounded-full text-sm font-medium"
              >
                {interest}
              </span>
            ))}
          </div>
        </section>
      </div>
    </div>
  );
};

export default ResumeContent;
