"use client";

import React, { createContext, useContext, useState } from 'react';

const ModalContext = createContext();

export const useModal = () => {
  const context = useContext(ModalContext);
  if (!context) {
    throw new Error('useModal must be used within a ModalProvider');
  }
  return context;
};

export const ModalProvider = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [modalType, setModalType] = useState(null);
  const [modalData, setModalData] = useState(null);

  const openModal = (type, data = null) => {
    setModalType(type);
    setModalData(data);
    setIsOpen(true);

    // Calculate scrollbar width more reliably
    const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;

    // Store the current scroll position
    const scrollY = window.scrollY;

    // Set CSS custom property for scrollbar width
    document.documentElement.style.setProperty('--scrollbar-width', `${scrollbarWidth}px`);

    // Store scroll position to restore later
    document.documentElement.style.setProperty('--scroll-y', `${scrollY}px`);

    // Prevent body scroll when modal is open without layout shift
    document.body.classList.add('modal-open');

    // Fix the body position to prevent scroll jumping
    document.body.style.position = 'fixed';
    document.body.style.top = `-${scrollY}px`;
    document.body.style.width = '100%';
  };

  const closeModal = () => {
    setIsOpen(false);

    // Get the stored scroll position
    const scrollY = document.documentElement.style.getPropertyValue('--scroll-y');

    // Restore body scroll without layout shift
    document.body.classList.remove('modal-open');

    // Remove fixed positioning
    document.body.style.position = '';
    document.body.style.top = '';
    document.body.style.width = '';

    // Restore scroll position
    if (scrollY) {
      window.scrollTo(0, parseInt(scrollY));
    }

    // Clean up CSS custom properties
    document.documentElement.style.removeProperty('--scrollbar-width');
    document.documentElement.style.removeProperty('--scroll-y');

    // Delay clearing modal type/data until after animation completes
    setTimeout(() => {
      setModalType(null);
      setModalData(null);
    }, 250); // Slightly longer than the 200ms animation
  };

  return (
    <ModalContext.Provider value={{
      isOpen,
      modalType,
      modalData,
      openModal,
      closeModal
    }}>
      {children}
    </ModalContext.Provider>
  );
};
