"use client";

import React, { createContext, useContext, useState } from 'react';

const ModalContext = createContext();

export const useModal = () => {
  const context = useContext(ModalContext);
  if (!context) {
    throw new Error('useModal must be used within a ModalProvider');
  }
  return context;
};

export const ModalProvider = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [modalType, setModalType] = useState(null);
  const [modalData, setModalData] = useState(null);

  const openModal = (type, data = null) => {
    setModalType(type);
    setModalData(data);
    setIsOpen(true);

    // Calculate scrollbar width to prevent layout shift
    const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;

    // Set CSS custom property for scrollbar width
    document.documentElement.style.setProperty('--scrollbar-width', `${scrollbarWidth}px`);

    // Prevent body scroll when modal is open without layout shift
    document.body.classList.add('modal-open');
  };

  const closeModal = () => {
    setIsOpen(false);

    // Restore body scroll without layout shift
    document.body.classList.remove('modal-open');

    // Clean up CSS custom property
    document.documentElement.style.removeProperty('--scrollbar-width');

    // Delay clearing modal type/data until after animation completes
    setTimeout(() => {
      setModalType(null);
      setModalData(null);
    }, 250); // Slightly longer than the 200ms animation
  };

  return (
    <ModalContext.Provider value={{
      isOpen,
      modalType,
      modalData,
      openModal,
      closeModal
    }}>
      {children}
    </ModalContext.Provider>
  );
};
